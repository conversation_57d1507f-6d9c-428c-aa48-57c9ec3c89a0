# 🚀 GitLab CI/CD Pipeline Configuration

This directory contains modular GitLab CI/CD pipeline configurations for the Atom Backoffice Data project.

## 📁 Directory Structure

```
.gitlab/ci/
├── README.md              # This file - pipeline documentation
├── branch-sync.yml        # Branch synchronization automation (active)
├── testing.yml.example    # Unit tests, E2E tests template
└── linting.yml.example    # Code quality, TypeScript linting template
```

## 🔄 Current Pipelines

### **Branch Synchronization (`branch-sync.yml`)**

**Purpose**: Automate branch synchronization for multi-branch workflow

**Flow**:
```
development ──(AUTO)──→ development-second
     │
     └──(MANUAL)──→ staging ──(MANUAL)──→ main (production)

🚨 Emergency: development-second ──(MANUAL)──→ development (reverse sync)
```

**Stages**:
1. **sync-dev-branches**: Auto-sync development → development-second + reverse sync (manual)
2. **sync-to-staging**: Manual sync development → staging
3. **sync-to-production**: Manual sync staging → main (production)

**Jobs**:
- ✅ **sync-to-dev-second**: Auto-sync development → development-second
- ⚠️ **sync-to-staging**: Manual sync development → staging
- ⚠️ **sync-to-main**: Manual sync staging → main
- 🚨 **reverse-sync-dev-second-to-dev**: Emergency reverse sync (cherry-pick)

**Triggers**:
- ✅ **Automatic**: sync-to-dev-second (push to development)
- ⚠️ **Manual**: sync-to-staging, sync-to-main (from development pipeline)
- 🚨 **Emergency**: reverse-sync (from development-second pipeline)

**Safety Features**:
- ✅ **Manual gates** for staging and production deployment
- ✅ **Cherry-pick approach** for reverse sync (selective commits)
- ✅ **Conflict detection** in reverse sync with abort mechanism
- ✅ **Skip CI tags** to prevent infinite loops
- ✅ **Emergency procedures** for critical fixes

## 🎯 Future Pipelines

### **Testing (`testing.yml`)** - *Template Available*
Based on `testing.yml.example`:
- Unit tests with Jest/Karma
- E2E tests setup
- Coverage reporting
- Test result artifacts

### **Linting (`linting.yml`)** - *Template Available*
Based on `linting.yml.example`:
- TypeScript compilation checks
- ESLint code quality
- Prettier formatting validation
- Commitlint message validation

### **Additional Pipelines** - *Planned*
- **Security scanning**: NPM audit, secret detection
- **Build optimization**: Bundle analysis, artifacts
- **Advanced deployment**: Blue-green patterns, notifications

## 🛠️ Usage

### **Main CI File**
The main `.gitlab-ci.yml` includes these modular configurations:

```yaml
include:
  - local: '.gitlab/ci/branch-sync.yml'    # ✅ Active
  # - local: '.gitlab/ci/testing.yml'      # Copy from .example when ready
  # - local: '.gitlab/ci/linting.yml'      # Copy from .example when ready
```

### **Adding New Pipelines**
1. Create new `.yml` file in `.gitlab/ci/`
2. Define stages and jobs
3. Include in main `.gitlab-ci.yml`
4. Update this README

### **Pipeline Variables**
Set in **GitLab Project Settings** → **CI/CD** → **Variables**:

| Variable | Purpose | Required |
|----------|---------|----------|
| `CI_PUSH_TOKEN` | Git operations authentication | ✅ |

## 📋 Best Practices

### **Modular Design**
- ✅ **Single responsibility** - Each file handles one concern
- ✅ **Reusable components** - Common patterns shared
- ✅ **Easy maintenance** - Changes isolated to specific files

### **Safety First**
- ✅ **Manual gates** for production operations
- ✅ **Resource groups** prevent concurrent dangerous operations
- ✅ **Rollback capabilities** for emergency recovery
- ✅ **Environment isolation** between dev/staging/production

### **Performance**
- ✅ **Conditional execution** - Jobs run only when needed
- ✅ **Parallel execution** - Independent jobs run concurrently
- ✅ **Caching strategies** - Reduce build times
- ✅ **Artifact management** - Efficient storage and retrieval

## 🔧 Troubleshooting

### **Common Issues**
1. **Missing CI_PUSH_TOKEN** - Add personal access token to project variables
2. **Merge conflicts** - Resolve manually and re-run pipeline
3. **Permission denied** - Check token scopes and branch protection rules
4. **Reverse sync conflicts** - Pipeline aborts, requires manual cherry-pick resolution
5. **Include local file errors** - Check file paths and YAML syntax

### **Debugging**
- Check **GitLab CI/CD** → **Pipelines** for detailed logs
- Use `echo` statements for debugging in scripts
- Test locally with `scripts/sync-branches.sh` first

### **Emergency Procedures**
- **Reverse sync**: Use `reverse-sync-dev-second-to-dev` job for critical fixes
- **Disable automation**: Comment out includes in main `.gitlab-ci.yml`
- **Manual sync**: Use local scripts as backup
- **Conflict resolution**: Manual cherry-pick when reverse sync fails

---

**Maintained by**: Development Team  
**Last Updated**: 2025-06-21  
**Review Schedule**: Monthly
